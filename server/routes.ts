import type { Express, Request, Response, NextFunction } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { apiKeySchema, documentAppendSchema, insertDocumentSchema, DocumentPage, DocumentImage } from "@shared/schema";
import { ZodError } from "zod";
import multer from "multer";
import fs from "fs";
import path from "path";
import { Mistral } from "@mistralai/mistralai";

// Helper function to convert Mistral OCR image objects to our DocumentImage format
function convertOcrImages(images: any[] | undefined | null): DocumentImage[] {
  if (!images) return [];
  
  return images.map(img => ({
    id: img.id || "",
    topLeftX: img.topLeftX != null ? img.topLeftX : undefined,
    topLeftY: img.topLeftY != null ? img.topLeftY : undefined,
    bottomRightX: img.bottomRightX != null ? img.bottomRightX : undefined,
    bottomRightY: img.bottomRightY != null ? img.bottomRightY : undefined,
    imageBase64: img.imageBase64 || undefined
  }));
}

// Shared API key configuration 
const SHARED_API_KEY = process.env.MISTRAL_API_KEY || "";
const RATE_LIMIT_WINDOW = 3600000; // 1 hour in milliseconds
const RATE_LIMIT_MAX = 10; // Maximum 10 requests per hour for shared key

// Simple in-memory rate limiter for shared API key
class RateLimiter {
  private requestCounts: Map<string, number> = new Map();
  private resetTimers: Map<string, NodeJS.Timeout> = new Map();

  isRateLimited(ip: string): boolean {
    const count = this.requestCounts.get(ip) || 0;
    return count >= RATE_LIMIT_MAX;
  }

  increment(ip: string): void {
    const count = this.requestCounts.get(ip) || 0;
    this.requestCounts.set(ip, count + 1);
    
    if (count === 0) {
      // Start a timer to reset this IP's count after the window period
      const timer = setTimeout(() => {
        this.requestCounts.delete(ip);
        this.resetTimers.delete(ip);
      }, RATE_LIMIT_WINDOW);
      
      this.resetTimers.set(ip, timer);
    }
  }

  getRemainingRequests(ip: string): number {
    const count = this.requestCounts.get(ip) || 0;
    return Math.max(0, RATE_LIMIT_MAX - count);
  }
}

// Configure multer for file uploads
const upload = multer({ 
  storage: multer.memoryStorage(),
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB limit
});

// Helper function for OCR processing
async function processFileWithOCR(fileBuffer: Buffer, mimeType: string, apiKey: string) {
  const client = new Mistral({ apiKey });
  const base64File = fileBuffer.toString("base64");
  const isImage = mimeType.startsWith("image/");
  const documentUrl = `data:${mimeType};base64,${base64File}`;
  
  return await client.ocr.process({
    model: "mistral-ocr-latest",
    document: isImage 
      ? { type: "image_url", imageUrl: documentUrl } 
      : { type: "document_url", documentUrl },
    includeImageBase64: true,
  });
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Initialize rate limiter for shared API key
  const rateLimiter = new RateLimiter();

  // Health check endpoint
  app.get("/health", (req, res) => {
    res.json({
      status: "ok",
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || "unknown"
    });
  });
  
  // Middleware to parse multipart form data
  const singleUpload = upload.single("file");
  const multipleUpload = upload.array("files", 10); // Allow up to 10 files
  
  // Endpoint to check shared API key status
  app.get("/api/shared-key-status", (req, res) => {
    const ip = req.ip || req.socket.remoteAddress || "unknown";
    const isAvailable = SHARED_API_KEY !== "";
    const isRateLimited = rateLimiter.isRateLimited(ip);
    const remaining = rateLimiter.getRemainingRequests(ip);
    
    console.log(`Shared key status for ${ip}: available=${isAvailable}, rateLimited=${isRateLimited}, remaining=${remaining}`);
    
    res.json({
      isAvailable,
      isRateLimited,
      remaining,
      maxRequests: RATE_LIMIT_MAX,
      windowHours: RATE_LIMIT_WINDOW / 3600000
    });
  });
  
  // API routes
  app.get("/api/documents", async (req, res) => {
    try {
      const documents = await storage.getAllDocuments();
      res.json(documents);
    } catch (error) {
      console.error("Error fetching documents:", error);
      res.status(500).json({ message: "Failed to fetch documents" });
    }
  });

  app.get("/api/documents/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid document ID" });
      }

      const document = await storage.getDocument(id);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }

      res.json(document);
    } catch (error) {
      console.error("Error fetching document:", error);
      res.status(500).json({ message: "Failed to fetch document" });
    }
  });

  app.post("/api/documents/upload", (req, res) => {
    singleUpload(req, res, async (err: any) => {
      if (err) {
        return res.status(400).json({ message: err.message });
      }

      try {
        // Check if file was uploaded
        if (!req.file) {
          return res.status(400).json({ message: "No file uploaded" });
        }
        
        // Validate API key from request or use shared key
        const { apiKey, useSharedKey } = apiKeySchema.parse(req.body);
        let actualApiKey = apiKey;
        
        // If user requested shared key
        if (useSharedKey === true) {
          // Check if shared key is available
          if (!SHARED_API_KEY) {
            return res.status(400).json({ 
              message: "Shared API key is not available. Please provide your own API key." 
            });
          }
          
          // Check rate limits
          const ip = req.ip || req.socket.remoteAddress || "unknown";
          if (rateLimiter.isRateLimited(ip)) {
            return res.status(429).json({ 
              message: "Rate limit exceeded for shared API key. Please try again later or use your own API key.",
              remainingTime: RATE_LIMIT_WINDOW / 1000 // seconds until reset
            });
          }
          
          // Use shared key and increment usage counter
          actualApiKey = SHARED_API_KEY;
          rateLimiter.increment(ip);
        } else if (!apiKey) {
          // If not using shared key and no API key provided
          return res.status(400).json({ 
            message: "API key is required. Either provide your own API key or use the shared key option." 
          });
        }

        // Initialize Mistral client with appropriate key
        const client = new Mistral({ apiKey: actualApiKey });

        // Process file with Mistral OCR
        const fileBuffer = req.file.buffer;
        const base64File = fileBuffer.toString("base64");
        const mimeType = req.file.mimetype;
        const isImage = mimeType.startsWith("image/");
        const documentUrl = `data:${mimeType};base64,${base64File}`;
        
        // Call Mistral OCR API
        const ocrResponse = await client.ocr.process({
          model: "mistral-ocr-latest",
          document: isImage 
            ? { type: "image_url", imageUrl: documentUrl } 
            : { type: "document_url", documentUrl },
          includeImageBase64: true,
        });

        if (!ocrResponse || !ocrResponse.pages) {
          return res.status(500).json({ message: "OCR processing failed" });
        }

        // Convert OCR response pages to our schema with zero-based indexing
        const documentPages: DocumentPage[] = ocrResponse.pages.map((page, idx) => ({
          // Use our own zero-based index to ensure proper ordering and lookup
          index: idx,
          markdown: page.markdown,
          images: convertOcrImages(page.images),
          dimensions: page.dimensions ? {
            dpi: page.dimensions.dpi,
            height: page.dimensions.height,
            width: page.dimensions.width
          } : undefined
        }));

        // Create document in storage
        const newDocument = await storage.createDocument({
          title: path.basename(req.file.originalname, path.extname(req.file.originalname)),
          originalFilename: req.file.originalname,
          pages: documentPages,
          totalPages: documentPages.length,
        });

        res.status(201).json(newDocument);
      } catch (error: any) {
        console.error("Error processing document:", error);
        
        if (error instanceof ZodError) {
          return res.status(400).json({ message: "Validation error", errors: error.errors });
        }
        
        if (error.message?.includes("401")) {
          return res.status(401).json({ message: "Invalid API key" });
        }
        
        res.status(500).json({ message: "Failed to process document" });
      }
    });
  });

  // Multiple file upload endpoint - creates one document from multiple files
  app.post("/api/documents/upload-multiple", (req, res) => {
    multipleUpload(req, res, async (err: any) => {
      if (err) {
        return res.status(400).json({ message: err.message });
      }

      try {
        // Check if files were uploaded
        if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
          return res.status(400).json({ message: "No files uploaded" });
        }
        
        // Validate API key from request or use shared key
        const { apiKey, useSharedKey } = apiKeySchema.parse(req.body);
        let actualApiKey = apiKey;
        
        // If user requested shared key
        if (useSharedKey === true) {
          // Check if shared key is available
          if (!SHARED_API_KEY) {
            return res.status(400).json({ 
              message: "Shared API key is not available. Please provide your own API key." 
            });
          }
          
          // Check rate limits
          const ip = req.ip || req.socket.remoteAddress || "unknown";
          if (rateLimiter.isRateLimited(ip)) {
            return res.status(429).json({ 
              message: "Rate limit exceeded for shared API key. Please try again later or use your own API key.",
              remainingTime: RATE_LIMIT_WINDOW / 1000 // seconds until reset
            });
          }
          
          // Use shared key and increment usage counter
          actualApiKey = SHARED_API_KEY;
          rateLimiter.increment(ip);
        } else if (!apiKey) {
          // If not using shared key and no API key provided
          return res.status(400).json({ 
            message: "API key is required. Either provide your own API key or use the shared key option." 
          });
        }

        // Initialize Mistral client with appropriate key
        const client = new Mistral({ apiKey: actualApiKey });

        // Process all files and collect pages
        const allPages: DocumentPage[] = [];
        let totalPageIndex = 0;
        
        for (const file of req.files) {
          // Process file with Mistral OCR
          const fileBuffer = file.buffer;
          const base64File = fileBuffer.toString("base64");
          const mimeType = file.mimetype;
          const isImage = mimeType.startsWith("image/");
          const documentUrl = `data:${mimeType};base64,${base64File}`;
          
          // Call Mistral OCR API
          const ocrResponse = await client.ocr.process({
            model: "mistral-ocr-latest",
            document: isImage 
              ? { type: "image_url", imageUrl: documentUrl } 
              : { type: "document_url", documentUrl },
            includeImageBase64: true,
          });

          if (!ocrResponse || !ocrResponse.pages) {
            throw new Error(`OCR processing failed for file: ${file.originalname}`);
          }

          // Convert OCR response pages to our schema and add to collection
          const filePages: DocumentPage[] = ocrResponse.pages.map((page) => ({
            index: totalPageIndex++,
            markdown: page.markdown,
            images: convertOcrImages(page.images),
            dimensions: page.dimensions ? {
              dpi: page.dimensions.dpi,
              height: page.dimensions.height,
              width: page.dimensions.width
            } : undefined
          }));

          allPages.push(...filePages);
        }

        // Create document title from first file or custom title
        const firstFile = req.files[0];
        const documentTitle = req.files.length === 1 
          ? path.basename(firstFile.originalname, path.extname(firstFile.originalname))
          : `Combined Document (${req.files.length} files)`;

        // Create document in storage with all pages
        const newDocument = await storage.createDocument({
          title: documentTitle,
          originalFilename: req.files.length === 1 
            ? firstFile.originalname 
            : `${req.files.length}_files_combined`,
          pages: allPages,
          totalPages: allPages.length,
        });

        res.status(201).json(newDocument);
      } catch (error: any) {
        console.error("Error processing multiple documents:", error);
        
        if (error instanceof ZodError) {
          return res.status(400).json({ message: "Validation error", errors: error.errors });
        }
        
        if (error.message?.includes("401")) {
          return res.status(401).json({ message: "Invalid API key" });
        }
        
        res.status(500).json({ message: "Failed to process documents" });
      }
    });
  });

  app.post("/api/documents/:id/append", (req, res) => {
    singleUpload(req, res, async (err: any) => {
      if (err) {
        return res.status(400).json({ message: err.message });
      }

      try {
        // Get document ID from URL
        const id = parseInt(req.params.id);
        if (isNaN(id)) {
          return res.status(400).json({ message: "Invalid document ID" });
        }

        // Check if file was uploaded
        if (!req.file) {
          return res.status(400).json({ message: "No file uploaded" });
        }
        
        // Check if document exists
        const document = await storage.getDocument(id);
        if (!document) {
          return res.status(404).json({ message: "Document not found" });
        }
        
        // Validate API key from request or use shared key
        const { apiKey, useSharedKey } = apiKeySchema.parse(req.body);
        let actualApiKey = apiKey;
        
        // If user requested shared key
        if (useSharedKey === true) {
          // Check if shared key is available
          if (!SHARED_API_KEY) {
            return res.status(400).json({ 
              message: "Shared API key is not available. Please provide your own API key." 
            });
          }
          
          // Check rate limits
          const ip = req.ip || req.socket.remoteAddress || "unknown";
          if (rateLimiter.isRateLimited(ip)) {
            return res.status(429).json({ 
              message: "Rate limit exceeded for shared API key. Please try again later or use your own API key.",
              remainingTime: RATE_LIMIT_WINDOW / 1000 // seconds until reset
            });
          }
          
          // Use shared key and increment usage counter
          actualApiKey = SHARED_API_KEY;
          rateLimiter.increment(ip);
        } else if (!apiKey) {
          // If not using shared key and no API key provided
          return res.status(400).json({ 
            message: "API key is required. Either provide your own API key or use the shared key option." 
          });
        }

        // Initialize Mistral client with appropriate key
        const client = new Mistral({ apiKey: actualApiKey });

        // Process file with Mistral OCR
        const fileBuffer = req.file.buffer;
        const base64File = fileBuffer.toString("base64");
        const mimeType = req.file.mimetype;
        const isImage = mimeType.startsWith("image/");
        const documentUrl = `data:${mimeType};base64,${base64File}`;
        
        // Call Mistral OCR API
        const ocrResponse = await client.ocr.process({
          model: "mistral-ocr-latest",
          document: isImage 
            ? { type: "image_url", imageUrl: documentUrl } 
            : { type: "document_url", documentUrl },
          includeImageBase64: true,
        });

        if (!ocrResponse || !ocrResponse.pages) {
          return res.status(500).json({ message: "OCR processing failed" });
        }

        // Convert OCR response pages to our schema while keeping zero-based indexing
        const documentPages: DocumentPage[] = ocrResponse.pages.map((page, idx) => ({
          // We don't set the index here, the storage.appendToDocument will handle it
          index: idx,
          markdown: page.markdown,
          images: convertOcrImages(page.images),
          dimensions: page.dimensions ? {
            dpi: page.dimensions.dpi,
            height: page.dimensions.height,
            width: page.dimensions.width
          } : undefined
        }));

        // Append pages to existing document
        const updatedDocument = await storage.appendToDocument(id, documentPages);
        if (!updatedDocument) {
          return res.status(404).json({ message: "Document not found" });
        }

        res.json(updatedDocument);
      } catch (error: any) {
        console.error("Error appending to document:", error);
        
        if (error instanceof ZodError) {
          return res.status(400).json({ message: "Validation error", errors: error.errors });
        }
        
        if (error.message?.includes("401")) {
          return res.status(401).json({ message: "Invalid API key" });
        }
        
        res.status(500).json({ message: "Failed to append to document" });
      }
    });
  });

  app.post("/api/documents/:id/append-multiple", (req, res) => {
    multipleUpload(req, res, async (err: any) => {
      if (err) {
        return res.status(400).json({ message: err.message });
      }

      try {
        // Get document ID from URL
        const id = parseInt(req.params.id);
        if (isNaN(id)) {
          return res.status(400).json({ message: "Invalid document ID" });
        }

        // Check if files were uploaded
        if (!req.files || req.files.length === 0) {
          return res.status(400).json({ message: "No files uploaded" });
        }
        
        // Check if document exists
        const document = await storage.getDocument(id);
        if (!document) {
          return res.status(404).json({ message: "Document not found" });
        }
        
        // Validate API key from request or use shared key
        const { apiKey, useSharedKey } = apiKeySchema.parse(req.body);
        let actualApiKey = apiKey;
        
        // If user requested shared key
        if (useSharedKey === true) {
          // Check if shared key is available
          if (!SHARED_API_KEY) {
            return res.status(400).json({ 
              message: "Shared API key is not available. Please provide your own API key." 
            });
          }
          
          // Check rate limits
          const ip = req.ip || req.socket.remoteAddress || "unknown";
          if (rateLimiter.isRateLimited(ip)) {
            return res.status(429).json({ 
              message: "Rate limit exceeded for shared API key. Please try again later or use your own API key.",
              remainingTime: RATE_LIMIT_WINDOW / 1000 // seconds until reset
            });
          }
          
          // Use shared key and increment usage counter
          actualApiKey = SHARED_API_KEY;
          rateLimiter.increment(ip);
        } else if (!apiKey) {
          // If not using shared key and no API key provided
          return res.status(400).json({ 
            message: "API key is required. Please provide your Mistral API key or enable the shared key option." 
          });
        }

        // Process all files with OCR and collect pages
        const allPages: DocumentPage[] = [];
        let totalPageIndex = document.totalPages; // Start indexing from existing page count

        const files = Array.isArray(req.files) ? req.files : [];
        for (const file of files) {
          // Call OCR API for each file
          const ocrResponse = await processFileWithOCR(file.buffer, file.mimetype, actualApiKey);
          
          if (!ocrResponse || !ocrResponse.pages) {
            throw new Error(`OCR processing failed for file: ${file.originalname}`);
          }

          // Convert OCR response pages to our schema and add to collection
          const filePages: DocumentPage[] = ocrResponse.pages.map((page: any) => ({
            index: totalPageIndex++,
            markdown: page.markdown,
            images: convertOcrImages(page.images),
            dimensions: page.dimensions ? {
              dpi: page.dimensions.dpi,
              height: page.dimensions.height,
              width: page.dimensions.width
            } : undefined
          }));

          allPages.push(...filePages);
        }

        // Append all pages to existing document
        const updatedDocument = await storage.appendToDocument(id, allPages);
        if (!updatedDocument) {
          return res.status(404).json({ message: "Document not found" });
        }

        res.json(updatedDocument);
      } catch (error: any) {
        console.error("Error appending multiple files to document:", error);
        
        if (error instanceof ZodError) {
          return res.status(400).json({ message: "Validation error", errors: error.errors });
        }
        
        if (error.message?.includes("401")) {
          return res.status(401).json({ message: "Invalid API key" });
        }
        
        res.status(500).json({ message: "Failed to append files to document" });
      }
    });
  });

  app.patch("/api/documents/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid document ID" });
      }

      // Simple validation for the title
      const { title } = req.body;
      if (!title || typeof title !== 'string' || title.trim() === '') {
        return res.status(400).json({ message: "Invalid title" });
      }

      // Update the document title
      const updatedDocument = await storage.updateDocument(id, { title: title.trim() });
      if (!updatedDocument) {
        return res.status(404).json({ message: "Document not found" });
      }

      res.json(updatedDocument);
    } catch (error) {
      console.error("Error updating document:", error);
      res.status(500).json({ message: "Failed to update document" });
    }
  });

  app.delete("/api/documents/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid document ID" });
      }

      const deleted = await storage.deleteDocument(id);
      if (!deleted) {
        return res.status(404).json({ message: "Document not found" });
      }

      res.status(204).send();
    } catch (error) {
      console.error("Error deleting document:", error);
      res.status(500).json({ message: "Failed to delete document" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}