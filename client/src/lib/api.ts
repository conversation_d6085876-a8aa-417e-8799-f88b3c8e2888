import { Document, OcrResponse } from "@shared/schema";
import { apiRequest } from "./queryClient";

// Shared API key status type
export interface SharedKeyStatus {
  isAvailable: boolean;
  isRateLimited: boolean;
  remaining: number;
  maxRequests: number;
  windowHours: number;
}

// Function to check shared API key status
export async function getSharedKeyStatus(): Promise<SharedKeyStatus> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
    
    const response = await fetch("/api/shared-key-status", {
      credentials: "include",
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error("Failed to get shared key status");
    }

    return response.json();
  } catch (error) {
    console.error("Failed to fetch shared key status:", error);
    // Return a fallback status that indicates the key is unavailable
    return {
      isAvailable: false,
      isRateLimited: false,
      remaining: 0,
      maxRequests: 10,
      windowHours: 1
    };
  }
}

export async function uploadDocument(file: File, apiKey?: string, useSharedKey?: boolean): Promise<Document> {
  const formData = new FormData();
  formData.append("file", file);
  
  // Either append API key or use shared key flag
  if (apiKey) {
    formData.append("apiKey", apiKey);
  }
  if (useSharedKey) {
    formData.append("useSharedKey", "true");
  }

  const response = await fetch("/api/documents/upload", {
    method: "POST",
    body: formData,
    credentials: "include",
  });

  if (!response.ok) {
    let errorMessage = "Failed to upload document";
    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorMessage;
    } catch (e) {
      // Ignore JSON parsing error
    }
    throw new Error(errorMessage);
  }

  return response.json();
}

export async function uploadMultipleDocuments(files: File[], apiKey?: string, useSharedKey?: boolean): Promise<Document> {
  const formData = new FormData();
  
  // Append all files
  files.forEach(file => {
    formData.append("files", file);
  });
  
  // Either append API key or use shared key flag
  if (apiKey) {
    formData.append("apiKey", apiKey);
  }
  if (useSharedKey) {
    formData.append("useSharedKey", "true");
  }

  const response = await fetch("/api/documents/upload-multiple", {
    method: "POST",
    body: formData,
    credentials: "include",
  });

  if (!response.ok) {
    let errorMessage = "Failed to upload documents";
    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorMessage;
    } catch (e) {
      // Ignore JSON parsing error
    }
    throw new Error(errorMessage);
  }

  return response.json();
}

export async function appendToDocument(documentId: number, file: File, apiKey?: string, useSharedKey?: boolean): Promise<Document> {
  const formData = new FormData();
  formData.append("file", file);
  
  // Either append API key or use shared key flag
  if (apiKey) {
    formData.append("apiKey", apiKey);
  }
  if (useSharedKey) {
    formData.append("useSharedKey", "true");
  }

  const response = await fetch(`/api/documents/${documentId}/append`, {
    method: "POST",
    body: formData,
    credentials: "include",
  });

  if (!response.ok) {
    let errorMessage = "Failed to append to document";
    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorMessage;
    } catch (e) {
      // Ignore JSON parsing error
    }
    throw new Error(errorMessage);
  }

  return response.json();
}

export async function appendMultipleToDocument(documentId: number, files: File[], apiKey?: string, useSharedKey?: boolean): Promise<Document> {
  const formData = new FormData();
  
  // Append all files
  files.forEach(file => {
    formData.append("files", file);
  });
  
  // Either append API key or use shared key flag
  if (apiKey) {
    formData.append("apiKey", apiKey);
  }
  if (useSharedKey) {
    formData.append("useSharedKey", "true");
  }

  const response = await fetch(`/api/documents/${documentId}/append-multiple`, {
    method: "POST",
    body: formData,
    credentials: "include",
  });

  if (!response.ok) {
    let errorMessage = "Failed to append files to document";
    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorMessage;
    } catch (e) {
      // Ignore JSON parsing error
    }
    throw new Error(errorMessage);
  }

  return response.json();
}

export async function updateDocument(id: number, data: { title: string }): Promise<Document> {
  return apiRequest<Document>("PATCH", `/api/documents/${id}`, data);
}

export async function deleteDocument(id: number): Promise<void> {
  await apiRequest<void>("DELETE", `/api/documents/${id}`);
}

export async function getAllDocuments(): Promise<Document[]> {
  const response = await fetch("/api/documents", {
    credentials: "include",
  });

  if (!response.ok) {
    throw new Error("Failed to fetch documents");
  }

  return response.json();
}

export async function getDocument(id: number): Promise<Document> {
  const response = await fetch(`/api/documents/${id}`, {
    credentials: "include",
  });

  if (!response.ok) {
    throw new Error("Failed to fetch document");
  }

  return response.json();
}
