import FileUpload from "@/components/file-upload";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader } from "@/components/ui/card";
import { FileText, Upload, ImageIcon, FileIcon } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface EmptyStateProps {
  onUpload: (file: File) => void;
  onMultipleUpload?: (files: File[]) => void;
  isUploading?: boolean;
}

export default function EmptyState({ onUpload, onMultipleUpload, isUploading = false }: EmptyStateProps) {
  return (
    <div className="h-full flex flex-col items-center justify-center px-4 py-8 animate-fade-in">
      <Card className="w-full mx-auto max-w-screen-md lg:max-w-screen-lg xl:max-w-screen-xl 2xl:max-w-[70%] shadow-sm bg-background">
        <CardHeader className="text-center pb-0">
          <div className="mx-auto bg-accent/10 rounded-full p-4 mb-4 w-24 h-24 flex items-center justify-center">
            <FileText className="h-12 w-12 text-accent" />
          </div>
          <h2 className="text-2xl font-medium md:text-3xl lg:text-4xl">Welcome!</h2>
          <p className="text-muted-foreground text-base mt-2 max-w-2xl mx-auto md:text-lg">
            Convert your documents to searchable markdown with AI-powered OCR
          </p>
        </CardHeader>
        
        <CardContent className="pt-8 px-6 sm:px-8 md:px-12 lg:px-16">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-6 mb-10">
            <div className="flex flex-col items-center text-center p-6 bg-muted/30 rounded-lg hover:bg-muted/40 transition-colors">
              <ImageIcon className="h-10 w-10 text-accent mb-3" />
              <h3 className="font-medium text-base mb-2 md:text-lg">Image Files</h3>
              <p className="text-sm text-muted-foreground md:text-base">JPG, PNG, JPEG, GIF</p>
            </div>
            <div className="flex flex-col items-center text-center p-6 bg-muted/30 rounded-lg hover:bg-muted/40 transition-colors">
              <FileIcon className="h-10 w-10 text-accent mb-3" />
              <h3 className="font-medium text-base mb-2 md:text-lg">Document Files</h3>
              <p className="text-sm text-muted-foreground md:text-base">PDF</p>
            </div>
          </div>
          
          <div className="max-w-4xl mx-auto">
            <FileUpload 
              onUpload={onUpload} 
              onMultipleUpload={onMultipleUpload}
              isUploading={isUploading}
              accept=".pdf,.jpg,.jpeg,.png,.gif" 
              buttonText="Choose files to upload"
              supportMultiple={true}
              className="w-full p-10 border-2 border-dashed border-accent/30 rounded-lg bg-accent/5 hover:bg-accent/10 transition-colors"
            />
          </div>
        </CardContent>
        
        <CardFooter className="text-center text-sm text-muted-foreground border-t py-4 md:text-base">
          <p className="w-full max-w-3xl mx-auto">
            Powered by Mistral AI's OCR technology. <br />
            You'll need an API key to process documents.
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}
