import { useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import { Upload, FileUp, ArrowUpCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { Progress } from "@/components/ui/progress";

interface FileUploadProps {
  onUpload: (file: File) => void;
  onMultipleUpload?: (files: File[]) => void;
  isUploading?: boolean;
  className?: string;
  accept?: string;
  buttonText?: string;
  supportMultiple?: boolean;
}

export default function FileUpload({
  onUpload,
  onMultipleUpload,
  isUploading = false,
  className = "",
  accept = ".pdf,.jpg,.jpeg,.png,.gif",
  buttonText = "Upload Document",
  supportMultiple = false
}: FileUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);
  
  const handleUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      if (files.length > 1 && onMultipleUpload) {
        // Multiple files selected - always use multiple handler if available
        onMultipleUpload(files);
      } else if (files.length === 1 && onMultipleUpload) {
        // Single file but multiple handler available - ask user preference
        onMultipleUpload(files);
      } else {
        // Fallback to single file handler
        onUpload(files[0]);
      }
    }
    // Reset input value so the same file can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      if (files.length > 1 && onMultipleUpload) {
        // Multiple files dropped - always use multiple handler if available
        onMultipleUpload(files);
      } else if (files.length === 1 && onMultipleUpload) {
        // Single file but multiple handler available
        onMultipleUpload(files);
      } else {
        // Fallback to single file handler
        onUpload(files[0]);
      }
    }
  };

  return (
    <div 
      className={cn("relative", className)}
      onDragEnter={handleDrag}
      onDragOver={handleDrag}
      onDragLeave={handleDrag}
      onDrop={handleDrop}
    >
      {isUploading ? (
        <div className="flex flex-col items-center justify-center h-full space-y-4 text-center py-4">
          <div className="h-12 w-12 rounded-full bg-accent/20 flex items-center justify-center animate-pulse">
            <FileUp className="h-6 w-6 text-accent animate-bounce" />
          </div>
          <div className="space-y-2 w-full">
            <h3 className="text-sm font-medium">Processing document...</h3>
            <div className="w-full">
              <Progress value={isUploading ? 60 : 0} className="h-2 w-full" />
            </div>
            <p className="text-xs text-muted-foreground">
              This may take a moment depending on file size
            </p>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center h-full space-y-4 text-center">
          {className ? (
            <>
              <ArrowUpCircle className="h-8 w-8 text-accent/70" />
              <div>
                <p className="text-sm font-medium">
                  {supportMultiple ? "Drag and drop files or click to browse" : "Drag and drop a file or click to browse"}
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  Supports PDF, JPG, PNG files{supportMultiple ? " (hold Ctrl/Cmd to select multiple)" : ""}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleUploadClick}
                className="border border-accent/30 text-accent/90 hover:text-accent hover:bg-accent/10"
              >
                <Upload className="h-4 w-4 mr-2" />
                {buttonText}
              </Button>
            </>
          ) : (
            <Button
              className="bg-accent hover:bg-accent/90 text-accent-foreground"
              onClick={handleUploadClick}
              disabled={isUploading}
            >
              <Upload className="h-4 w-4 mr-2" />
              {buttonText}
            </Button>
          )}
        </div>
      )}
      
      <input
        type="file"
        className="hidden"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept={accept}
        multiple={supportMultiple}
      />
      
      {dragActive && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="card p-8 max-w-sm mx-auto animate-slide-in">
            <div className="mb-6 bg-accent/10 w-20 h-20 rounded-full flex items-center justify-center mx-auto">
              <ArrowUpCircle className="h-10 w-10 text-accent" />
            </div>
            <h3 className="text-xl font-medium mb-2 text-center">Drop to Upload</h3>
            <p className="text-muted-foreground mb-4 text-center text-sm">
              Release to upload your document and process with Mistral OCR
            </p>
            <p className="text-xs text-muted-foreground text-center">
              Supports PDF, JPEG, PNG files
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
