import React, { useEffect, useState } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { DocumentImage } from "@shared/schema";

interface MarkdownRendererProps {
  markdown: string;
  images?: DocumentImage[];
}

// Detect if the content is a table of contents
const isTOC = (content: string): boolean => {
  // Check if content has many numbered sections
  const numberedSectionRegex = /^\d+(\.\d+)*\.\s/gm;
  const matches = content.match(numberedSectionRegex);
  return matches ? matches.length > 5 : false;
};

// Function to preprocess a table of contents for better formatting
const preprocessTOC = (content: string): string => {
  // First check if it's likely a TOC
  if (!isTOC(content)) return content;
  
  // Replace numbered sections with proper markdown list items and add proper indentation
  let lines = content.split('\n');
  let processedLines = lines.map(line => {
    // Match section numbers like 1., 1.1., 1.1.1., etc.
    const sectionMatch = line.match(/^(\d+(\.\d+)*)\.\s/);
    
    if (!sectionMatch) return line;
    
    const sectionNumber = sectionMatch[1];
    const depth = sectionNumber.split('.').length;
    const indent = '  '.repeat(depth - 1);
    const pageNumMatch = line.match(/\.{3,}\s*(\d+)$/);
    const pageNum = pageNumMatch ? ` (p. ${pageNumMatch[1]})` : '';
    
    // Replace with proper markdown list item with indentation
    return `${indent}- **${sectionNumber}**${line.replace(sectionMatch[0], ' ')}${pageNum}`;
  });
  
  // Add a header to clarify this is a table of contents
  processedLines.unshift('## Table of Contents\n');
  
  return processedLines.join('\n');
};

export default function MarkdownRenderer({ markdown, images = [] }: MarkdownRendererProps) {
  const [processedContent, setProcessedContent] = useState(markdown);
  
  useEffect(() => {
    // Process the markdown content
    let content = markdown;
    
    // If it looks like a table of contents, format it better
    if (isTOC(content)) {
      content = preprocessTOC(content);
    }
    
    // Process images to add them to the markdown
    if (images && images.length > 0) {
      console.log("Processing images:", images);
      
      // Add image elements to the end of the markdown content
      let imageMarkdown = "\n\n";
      
      images.forEach((image, index) => {
        // Check for both snake_case and camelCase properties
        const imageId = image.id || "";
        const imageBase64 = image.image_base64 || image.imageBase64;
        
        if (imageId && imageBase64) {
          // Add an image tag for each image
          imageMarkdown += `![Image ${index+1}](${imageId})\n\n`;
        }
      });
      
      // Append the image markdown to the original content
      content += imageMarkdown;
    }
    
    setProcessedContent(content);
  }, [markdown, images]);
  
  // Create a Map for quick image lookup
  const imageMap = new Map<string, string>();
  
  // Populate the image map
  if (images && images.length > 0) {
    images.forEach(image => {
      const imageId = image.id || "";
      const imageBase64 = image.image_base64 || image.imageBase64;
      
      if (imageId && imageBase64) {
        imageMap.set(imageId, imageBase64);
      }
    });
  }
  
  // Custom renderer components
  const components = {
    img: ({ src, alt, ...props }: React.ImgHTMLAttributes<HTMLImageElement>) => {
      // Check if we have a base64 image with this ID
      const imageBase64 = imageMap.get(src || "");
      
      if (imageBase64) {
        // Check if the base64 data already includes the data:image prefix
        const imgSrc = imageBase64.startsWith('data:') 
          ? imageBase64 
          : `data:image/jpeg;base64,${imageBase64}`;
          
        return (
          <figure className="my-6 mx-auto max-w-[90%]">
            <div className="bg-muted/20 rounded-lg overflow-hidden border border-border flex justify-center">
              <img 
                src={imgSrc} 
                alt={alt || "Document image"} 
                className="max-w-full h-auto object-contain" 
                loading="lazy"
              />
            </div>
            {alt && (
              <figcaption className="text-xs text-muted-foreground text-center mt-2">
                {alt}
              </figcaption>
            )}
          </figure>
        );
      }
      
      // Fallback to the regular src
      return (
        <figure className="my-6 mx-auto max-w-[90%]">
          <div className="bg-muted/20 rounded-lg overflow-hidden border border-border flex justify-center">
            <img 
              src={src} 
              alt={alt || "Image"} 
              className="max-w-full h-auto object-contain" 
              loading="lazy"
            />
          </div>
          {alt && (
            <figcaption className="text-xs text-muted-foreground text-center mt-2">
              {alt}
            </figcaption>
          )}
        </figure>
      );
    },
    table: (props: any) => (
      <div className="overflow-x-auto my-6 rounded-lg border border-border">
        <table className="w-full border-collapse" {...props} />
      </div>
    ),
    th: (props: any) => (
      <th className="border-b border-r last:border-r-0 border-border bg-muted/30 px-4 py-2.5 text-left text-sm font-medium" {...props} />
    ),
    td: (props: any) => (
      <td className="border-b border-r last:border-r-0 border-border px-4 py-2.5 text-sm" {...props} />
    ),
    pre: (props: any) => (
      <pre className="p-4 bg-muted/20 border border-border rounded-lg overflow-x-auto my-4 text-sm" {...props} />
    ),
    code: ({ className, ...props }: any) => (
      <code className={className || "bg-muted/30 px-1.5 py-0.5 rounded text-sm font-mono"} {...props} />
    ),
    blockquote: (props: any) => (
      <blockquote className="border-l-4 border-accent/40 pl-4 italic text-muted-foreground my-4" {...props} />
    ),
    hr: (props: any) => (
      <hr className="my-6 border-t border-border" {...props} />
    ),
    // Improve list rendering
    li: (props: any) => {
      // Check if this list item contains a section heading (from TOC preprocessing)
      const hasSectionHeading = props.children?.some?.(
        (child: any) => typeof child === 'string' && child.match(/^\*\*\d+(\.\d+)*\*\*/)
      );
      
      return (
        <li className={hasSectionHeading ? "my-2" : "my-1"} {...props} />
      );
    },
    // Better handle heading spacing in tables of contents
    h2: (props: any) => (
      <h2 className="text-xl font-bold mt-6 mb-4" {...props} />
    )
  };

  return (
    <div className="markdown-content prose prose-stone max-w-none dark:prose-invert prose-headings:font-semibold prose-h1:text-2xl prose-h2:text-xl prose-h3:text-lg prose-h4:text-base prose-h5:text-sm prose-h6:text-sm">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={components}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  );
}
