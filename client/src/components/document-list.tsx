import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Document } from "@shared/schema";
import { useState, useRef, useEffect } from "react";
import { 
  Upload, 
  FileIcon, 
  LockIcon, 
  Calendar, 
  Clock, 
  ChevronRight,
  FileText,
  Trash2,
  Check,
  Info,
  Loader2,
  KeyRound,
  PencilIcon
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { 
  Tooltip, 
  TooltipContent, 
  TooltipTrigger,
  TooltipProvider 
} from "@/components/ui/tooltip";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { deleteDocument, getSharedKeyStatus, SharedKeyStatus } from "@/lib/api";
import { queryClient } from "@/lib/queryClient";

interface DocumentListProps {
  documents: Document[];
  isLoading: boolean;
  selectedDocument: Document | null;
  onSelectDocument: (document: Document) => void;
  onUpload: (file: File) => Promise<void>;
  onDeleteDocument?: (id: number) => Promise<void>;
  onRenameDocument?: (id: number, newTitle: string) => Promise<void>;
  apiKey: string;
  onApiKeyChange: (apiKey: string) => void;
  useSharedKey?: boolean;
  onUseSharedKeyChange?: (useShared: boolean) => void;
  refreshSharedKeyStatusRef?: React.MutableRefObject<(() => void) | null>;
}

export default function DocumentList({
  documents,
  isLoading,
  selectedDocument,
  onSelectDocument,
  onUpload,
  onDeleteDocument,
  onRenameDocument,
  apiKey,
  onApiKeyChange,
  useSharedKey = false,
  onUseSharedKeyChange,
  refreshSharedKeyStatusRef
}: DocumentListProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [localApiKey, setLocalApiKey] = useState(apiKey);
  const [isInputFocused, setIsInputFocused] = useState(false);
  const [isDeleting, setIsDeleting] = useState<number | null>(null);
  const [isRenaming, setIsRenaming] = useState<number | null>(null);
  const [newTitle, setNewTitle] = useState<string>("");
  const [sharedKeyStatus, setSharedKeyStatus] = useState<SharedKeyStatus | null>(null);
  const [isLoadingStatus, setIsLoadingStatus] = useState(false);
  const { toast } = useToast();
  
  // Function to fetch shared key status
  const fetchSharedKeyStatus = async () => {
    try {
      setIsLoadingStatus(true);
      const status = await getSharedKeyStatus();
      setSharedKeyStatus(status);
    } catch (error) {
      console.error("Failed to fetch shared key status:", error);
      // Set a fallback status to avoid UI issues in case of connection error
      if (!sharedKeyStatus) {
        setSharedKeyStatus({
          isAvailable: false,
          isRateLimited: false,
          remaining: 0,
          maxRequests: 10,
          windowHours: 1
        });
      }
      // Don't show the loading indicator for too long on error
      setTimeout(() => setIsLoadingStatus(false), 500);
    } finally {
      setIsLoadingStatus(false);
    }
  };
  
  // Expose the fetchSharedKeyStatus function to the parent component
  useEffect(() => {
    // If the parent provided a ref, assign our fetchSharedKeyStatus function to it
    if (refreshSharedKeyStatusRef) {
      refreshSharedKeyStatusRef.current = fetchSharedKeyStatus;
    }
  }, []);
  
  // Fetch shared key status on component mount
  useEffect(() => {
    // Initial fetch
    fetchSharedKeyStatus();
    
    // Set up a refresh interval (every 10 seconds)
    const refreshInterval = setInterval(() => {
      if (useSharedKey) {
        fetchSharedKeyStatus();
      }
    }, 10000);
    
    // Set up a reconnection strategy in case of connection issues
    const reconnectionTimer = setTimeout(() => {
      // If we still don't have status after 3 seconds, try again
      if (!sharedKeyStatus && useSharedKey) {
        fetchSharedKeyStatus();
      }
    }, 3000);
    
    return () => {
      clearInterval(refreshInterval);
      clearTimeout(reconnectionTimer);
    };
  }, [useSharedKey, sharedKeyStatus]);
  
  const handleUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      await onUpload(file);
      
      // Refresh shared key status after upload if using shared key
      if (useSharedKey) {
        fetchSharedKeyStatus();
      }
    }
    // Reset input value so the same file can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSaveApiKey = () => {
    onApiKeyChange(localApiKey);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSaveApiKey();
    }
  };

  const formatDate = (dateString: string | Date) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    }).format(date);
  };

  return (
    <div className="flex flex-col h-full">
      {/* Shared API Key Toggle */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-3">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center text-sm font-medium">
                  <KeyRound className="h-4 w-4 mr-1.5 text-accent" />
                  Use Shared API Key
                </div>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p className="text-xs max-w-[220px]">
                  Use our shared API key (limited to {sharedKeyStatus?.maxRequests || 10} 
                  requests per {sharedKeyStatus?.windowHours || 1} hour)
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <div className="flex items-center">
            <Switch 
              id="shared-key" 
              checked={useSharedKey}
              disabled={!sharedKeyStatus?.isAvailable}
              onCheckedChange={(checked) => {
                if (onUseSharedKeyChange) {
                  onUseSharedKeyChange(checked);
                  
                  // Show appropriate toast message
                  if (checked) {
                    toast({
                      title: "Using shared API key",
                      description: `${sharedKeyStatus?.remaining || 0} requests remaining in this period.`
                    });
                  }
                }
              }}
            />
          </div>
        </div>
        
        {sharedKeyStatus && (
          <div className={cn(
            "text-xs rounded p-2 mb-2",
            sharedKeyStatus.isAvailable 
              ? sharedKeyStatus.remaining > 0 
                ? "bg-success/10 text-success" 
                : "bg-warning/10 text-warning"
              : "bg-destructive/10 text-destructive"
          )}>
            {!sharedKeyStatus.isAvailable ? (
              <p className="flex items-center">
                <Info className="h-3 w-3 mr-1" />
                Shared API key is not available
              </p>
            ) : sharedKeyStatus.isRateLimited ? (
              <p className="flex items-center">
                <Info className="h-3 w-3 mr-1" />
                Rate limit exceeded. Try again later or use your own API key.
              </p>
            ) : (
              <p className="flex items-center">
                <Info className="h-3 w-3 mr-1" />
                {sharedKeyStatus.remaining} of {sharedKeyStatus.maxRequests} requests remaining
              </p>
            )}
          </div>
        )}
        
        {isLoadingStatus && !sharedKeyStatus && (
          <div className="flex items-center justify-center text-xs text-muted-foreground mb-2">
            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
            Checking shared key status...
          </div>
        )}
      </div>
      
      {/* API Key Input (only shown when not using shared key) */}
      {!useSharedKey && (
        <div className="p-4 border-b border-border">
          <div className="flex items-center justify-between mb-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center text-sm font-medium">
                    <LockIcon className="h-4 w-4 mr-1.5 text-accent" />
                    Your API Key
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs max-w-[220px]">
                    Enter your Mistral AI API key to process documents
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            {apiKey && (
              <span className="text-xs px-1.5 py-0.5 rounded-full bg-success/20 text-success">
                Saved
              </span>
            )}
          </div>
          
          <div className="relative">
            <Input
              type={isInputFocused ? "text" : "password"}
              id="api-key"
              className="w-full text-sm bg-background"
              placeholder="Enter your Mistral API key"
              value={localApiKey}
              onChange={(e) => setLocalApiKey(e.target.value)}
              onKeyPress={handleKeyPress}
              onFocus={() => setIsInputFocused(true)}
              onBlur={() => {
                setIsInputFocused(false);
                handleSaveApiKey();
              }}
            />
          </div>
        </div>
      )}
      
      {/* Upload Button */}
      <div className="p-4 border-b border-border">
        <Button
          className="w-full bg-accent hover:bg-accent/90 text-accent-foreground"
          onClick={handleUploadClick}
        >
          <Upload className="h-4 w-4 mr-2" />
          Upload Document
        </Button>
        <input
          type="file"
          className="hidden"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".pdf,.jpg,.jpeg,.png,.gif"
        />
        <p className="text-xs text-muted-foreground mt-2 text-center">
          Supports PDF, JPG, PNG
        </p>
      </div>

      {/* Document List */}
      <div className="flex-1 overflow-y-auto">
        <div className="sticky top-0 z-10 px-4 py-2 border-b border-border bg-muted/50">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium">Recent Documents</h3>
            <span className="text-xs text-muted-foreground">{documents.length || 0} items</span>
          </div>
        </div>
        
        <div className="p-4">
          {isLoading ? (
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="p-3 bg-background rounded-lg border border-border">
                  <div className="flex items-start">
                    <Skeleton className="h-10 w-10 rounded-md mr-3" />
                    <div className="flex-grow">
                      <Skeleton className="h-5 w-32 mb-2" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : documents.length === 0 ? (
            <div className="text-center p-8 text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-3 text-muted-foreground/50" />
              <p className="font-medium mb-1">No documents yet</p>
              <p className="text-sm">Upload a document to get started</p>
            </div>
          ) : (
            <div className="space-y-3">
              {documents.map((doc) => (
                <div 
                  key={doc.id}
                  className={cn(
                    "card card-hover p-3 relative",
                    selectedDocument?.id === doc.id ? 
                      "ring-2 ring-accent border-transparent" : 
                      "border-border"
                  )}
                >
                  <div className="flex items-start">
                    <div 
                      className="h-10 w-10 rounded-md flex items-center justify-center bg-accent/10 text-accent mr-3 flex-shrink-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        onSelectDocument(doc);
                      }}
                    >
                      <FileText className="h-5 w-5" />
                    </div>
                    
                    <div 
                      className="flex-grow min-w-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        onSelectDocument(doc);
                      }}
                    >
                      <h4 className="font-medium text-sm truncate">{doc.title}</h4>
                      <div className="flex items-center mt-1 text-xs text-muted-foreground">
                        <Calendar className="h-3 w-3 mr-1" /> 
                        <span className="truncate">{formatDate(doc.createdAt)}</span>
                        <Separator orientation="vertical" className="mx-2 h-3" />
                        <span>{doc.totalPages} page{doc.totalPages !== 1 ? 's' : ''}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center ml-1 space-x-1" onClick={(e) => e.stopPropagation()}>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            className="h-8 w-8 rounded-full hover:bg-muted"
                          >
                            <ChevronRight className="h-4 w-4 text-muted-foreground" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-[180px]">
                          <DropdownMenuLabel>Document Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => onSelectDocument(doc)}
                            className="flex items-center cursor-pointer"
                          >
                            <FileText className="mr-2 h-4 w-4" />
                            <span>View Document</span>
                          </DropdownMenuItem>
                          
                          {/* Rename menu item */}
                          {isRenaming === doc.id ? (
                            <DropdownMenuItem 
                              className="flex items-center cursor-pointer"
                              onSelect={(e) => e.preventDefault()} // Prevent auto-close
                            >
                              <div className="w-full flex items-center space-x-2">
                                <Input
                                  className="h-8 text-sm flex-1"
                                  value={newTitle}
                                  onChange={(e) => setNewTitle(e.target.value)}
                                  placeholder="Enter new title"
                                  onKeyDown={(e) => {
                                    if (e.key === "Enter" && newTitle.trim()) {
                                      e.preventDefault();
                                      if (onRenameDocument) {
                                        onRenameDocument(doc.id, newTitle.trim());
                                        setIsRenaming(null);
                                        setNewTitle("");
                                      }
                                    } else if (e.key === "Escape") {
                                      setIsRenaming(null);
                                      setNewTitle("");
                                    }
                                  }}
                                  autoFocus
                                />
                                <Button 
                                  variant="ghost" 
                                  size="sm"
                                  className="h-8 px-2"
                                  onClick={() => {
                                    if (newTitle.trim() && onRenameDocument) {
                                      onRenameDocument(doc.id, newTitle.trim());
                                      setIsRenaming(null);
                                      setNewTitle("");
                                    }
                                  }}
                                >
                                  <Check className="h-4 w-4" />
                                </Button>
                              </div>
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem 
                              className="flex items-center cursor-pointer"
                              onClick={() => {
                                setIsRenaming(doc.id);
                                setNewTitle(doc.title);
                              }}
                            >
                              <PencilIcon className="mr-2 h-4 w-4" />
                              <span>Rename</span>
                            </DropdownMenuItem>
                          )}
                          
                          <DropdownMenuItem
                            className="flex items-center cursor-pointer text-destructive focus:text-destructive"
                            onClick={async () => {
                              if (confirm("Are you sure you want to delete this document?")) {
                                try {
                                  setIsDeleting(doc.id);
                                  await deleteDocument(doc.id);
                                  queryClient.invalidateQueries({ queryKey: ['/api/documents'] });
                                  toast({
                                    title: "Document deleted",
                                    description: "Document has been permanently deleted.",
                                  });
                                } catch (error) {
                                  toast({
                                    title: "Error deleting document",
                                    description: "Failed to delete the document. Please try again.",
                                    variant: "destructive"
                                  });
                                } finally {
                                  setIsDeleting(null);
                                }
                              }
                            }}
                            disabled={isDeleting === doc.id}
                          >
                            {isDeleting === doc.id ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                <span>Deleting...</span>
                              </>
                            ) : (
                              <>
                                <Trash2 className="mr-2 h-4 w-4" />
                                <span>Delete</span>
                              </>
                            )}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}