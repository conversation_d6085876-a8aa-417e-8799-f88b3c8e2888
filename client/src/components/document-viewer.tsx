import { Document } from "@shared/schema";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import MarkdownRenderer from "@/components/ui/markdown-renderer";
import { 
  Download, 
  Copy, 
  Plus, 
  ChevronLeft, 
  ChevronRight, 
  Code, 
  Eye, 
  FileUp,
  CornerRightDown,
  Calendar,
  File,
  Loader2,
  BookOpen,
  Layers
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useState, useRef } from "react";
import { cn } from "@/lib/utils";
import { Progress } from "@/components/ui/progress";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider
} from "@/components/ui/tooltip";

interface DocumentViewerProps {
  document: Document;
  currentPage: number;
  setCurrentPage: (page: number) => void;
  viewMode: "rendered" | "raw";
  setViewMode: (mode: "rendered" | "raw") => void;
  onAppend: (file: File) => Promise<void>;
  onAppendMultiple?: (files: File[]) => Promise<void>;
  isProcessing: boolean;
}

export default function DocumentViewer({
  document,
  currentPage,
  setCurrentPage,
  viewMode,
  setViewMode,
  onAppend,
  onAppendMultiple,
  isProcessing
}: DocumentViewerProps) {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);
  const [displayMode, setDisplayMode] = useState<"pages" | "consolidated">("pages");
  
  // Get current page content 
  // The page indices are zero-based, but UI is 1-based
  const currentPageData = document.pages.find(page => page.index === currentPage - 1);
  
  // Prepare consolidated content
  const consolidatedMarkdown = document.pages
    .sort((a, b) => a.index - b.index)
    .map(page => page.markdown)
    .join('\n\n---\n\n');
  
  const consolidatedImages = document.pages
    .sort((a, b) => a.index - b.index)
    .flatMap(page => page.images);
  
  // Debugging to help see what's available
  console.log('Document pages:', document.pages);
  console.log('Current page:', currentPage);
  console.log('Looking for index:', currentPage - 1);
  console.log('Current page data:', currentPageData);
  
  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < document.totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handleCopyToClipboard = () => {
    const content = displayMode === "consolidated" ? consolidatedMarkdown : currentPageData?.markdown;
    if (!content) return;
    
    navigator.clipboard.writeText(content)
      .then(() => {
        toast({
          title: "Copied to clipboard",
          description: displayMode === "consolidated" 
            ? "All pages have been copied to your clipboard." 
            : "The markdown content has been copied to your clipboard."
        });
      })
      .catch(() => {
        toast({
          title: "Copy failed",
          description: "Failed to copy to clipboard.",
          variant: "destructive"
        });
      });
  };

  const handleDownloadMarkdown = () => {
    const content = displayMode === "consolidated" ? consolidatedMarkdown : currentPageData?.markdown;
    if (!content) return;
    
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = window.document.createElement('a');
    a.href = url;
    a.download = displayMode === "consolidated" 
      ? `${document.title}-all-pages.md`
      : `${document.title}-page-${currentPage}.md`;
    window.document.body.appendChild(a);
    a.click();
    window.document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast({
      title: "Download started",
      description: "Your markdown file is being downloaded."
    });
  };

  const handleAppendClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      if (files.length > 1 && onAppendMultiple) {
        onAppendMultiple(files);
      } else if (files.length === 1 && onAppendMultiple) {
        onAppendMultiple(files);
      } else {
        onAppend(files[0]);
      }
    }
    // Reset input value so the same files can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      if (files.length > 1 && onAppendMultiple) {
        onAppendMultiple(files);
      } else if (files.length === 1 && onAppendMultiple) {
        onAppendMultiple(files);
      } else {
        onAppend(files[0]);
      }
    }
  };

  const formatDate = (dateString: string | Date) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <div 
      className="flex flex-col h-full"
      onDragEnter={handleDrag}
      onDragOver={handleDrag}
      onDragLeave={handleDrag}
      onDrop={handleDrop}
    >
      {isProcessing ? (
        <div className="h-full flex flex-col items-center justify-center p-6 animate-fade-in">
          <Card className="max-w-md md:max-w-lg lg:max-w-2xl xl:max-w-3xl w-full">
            <CardContent className="pt-8 pb-8 px-6 md:px-10">
              <div className="flex flex-col items-center text-center space-y-6">
                <div className="h-16 w-16 md:h-20 md:w-20 lg:h-24 lg:w-24 rounded-full bg-accent/20 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 md:h-10 md:w-10 lg:h-12 lg:w-12 text-accent animate-spin" />
                </div>
                <div>
                  <h3 className="text-lg md:text-xl lg:text-2xl font-medium mb-2 md:mb-3">Processing Document</h3>
                  <p className="text-muted-foreground text-sm md:text-base lg:text-lg mb-4 max-w-md mx-auto">
                    Converting your document to markdown with Mistral OCR...
                  </p>
                </div>
                <div className="w-full md:w-4/5 lg:w-2/3 mx-auto space-y-3">
                  <Progress value={65} className="h-2 md:h-3" />
                  <p className="text-xs md:text-sm text-muted-foreground">
                    This may take a moment depending on document size
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      ) : currentPageData ? (
        <div className="flex flex-col h-full">
          {/* Document metadata section */}
          <div className="border-b border-border">
            <div className="container-md mx-auto py-4">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center">
                    <File className="h-5 w-5 text-accent mr-2 flex-shrink-0" />
                    <h1 className="text-xl font-medium truncate">Document Details</h1>
                  </div>
                  <div className="flex items-center mt-1 text-sm text-muted-foreground">
                    <span className="truncate">{document.originalFilename}</span>
                    <span className="mx-1.5">•</span>
                    <span className="whitespace-nowrap">
                      {document.totalPages} {document.totalPages === 1 ? 'page' : 'pages'}
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center gap-2 flex-shrink-0">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={handleAppendClick}
                          disabled={isProcessing}
                          className="text-sm"
                        >
                          <CornerRightDown className="h-4 w-4 mr-1.5" />
                          Append
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="text-xs">Add more pages to this document</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    className="hidden"
                    accept=".pdf,.jpg,.jpeg,.png,.gif"
                    multiple
                  />
                </div>
              </div>
            </div>
          </div>
          
          {/* Main content */}
          <div className="flex-1 overflow-auto">
            <div className="w-full px-4 mx-auto py-6 md:px-6 lg:px-8 xl:px-12 max-w-full lg:max-w-[90%] xl:max-w-[85%] 2xl:max-w-[80%]">
              <Card className="mb-6 shadow-sm mx-auto">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center text-base justify-between">
                    <div className="flex items-center text-sm md:text-base">
                      <Calendar className="h-4 w-4 mr-2 text-muted-foreground md:h-5 md:w-5" />
                      Processed {formatDate(document.createdAt)}
                    </div>
                    
                    <div className="flex space-x-1 md:space-x-2">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              className="h-8 w-8 p-0 md:h-9 md:w-9" 
                              onClick={handleCopyToClipboard}
                            >
                              <Copy className="h-4 w-4 md:h-5 md:w-5" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs md:text-sm">
                              {displayMode === "consolidated" ? "Copy all pages" : "Copy current page"}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              className="h-8 w-8 p-0 md:h-9 md:w-9" 
                              onClick={handleDownloadMarkdown}
                            >
                              <Download className="h-4 w-4 md:h-5 md:w-5" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs md:text-sm">
                              {displayMode === "consolidated" ? "Download all pages" : "Download current page"}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </CardTitle>
                </CardHeader>
                
                {/* Display Mode Tabs */}
                <div className="px-6 pb-3">
                  <Tabs value={displayMode} onValueChange={(value) => setDisplayMode(value as "pages" | "consolidated")}>
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="pages" className="flex items-center gap-2">
                        <BookOpen className="h-4 w-4" />
                        Page View
                      </TabsTrigger>
                      <TabsTrigger value="consolidated" className="flex items-center gap-2">
                        <Layers className="h-4 w-4" />
                        All Pages
                      </TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>
                
                <CardContent className="pt-0">
                  {displayMode === "pages" ? (
                    <>
                      {viewMode === "rendered" ? (
                        <div className="document-content prose prose-sm md:prose-base lg:prose-lg xl:prose-xl max-w-none">
                          <MarkdownRenderer markdown={currentPageData.markdown} images={currentPageData.images} />
                        </div>
                      ) : (
                        <div className="font-mono text-sm md:text-base whitespace-pre-wrap bg-muted/30 p-4 rounded-md overflow-auto min-h-[400px] md:min-h-[600px] h-full">
                          {currentPageData.markdown}
                        </div>
                      )}
                    </>
                  ) : (
                    <>
                      {viewMode === "rendered" ? (
                        <div className="document-content prose prose-sm md:prose-base lg:prose-lg xl:prose-xl max-w-none">
                          <MarkdownRenderer markdown={consolidatedMarkdown} images={consolidatedImages} />
                        </div>
                      ) : (
                        <div className="font-mono text-sm md:text-base whitespace-pre-wrap bg-muted/30 p-4 rounded-md overflow-auto min-h-[400px] md:min-h-[600px] h-full">
                          {consolidatedMarkdown}
                        </div>
                      )}
                    </>
                  )}
                </CardContent>
                
                {document.totalPages > 1 && displayMode === "pages" && (
                  <CardFooter className="flex items-center justify-between border-t pt-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="gap-1 text-xs"
                      disabled={currentPage <= 1}
                      onClick={handlePreviousPage}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>
                    
                    <span className="text-sm text-muted-foreground">
                      Page {currentPage} of {document.totalPages}
                    </span>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      className="gap-1 text-xs"
                      disabled={currentPage >= document.totalPages}
                      onClick={handleNextPage}
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </CardFooter>
                )}
                
                {displayMode === "consolidated" && (
                  <CardFooter className="flex items-center justify-center border-t pt-4">
                    <span className="text-sm text-muted-foreground">
                      Showing all {document.totalPages} {document.totalPages === 1 ? 'page' : 'pages'}
                    </span>
                  </CardFooter>
                )}
              </Card>
            </div>
          </div>
        </div>
      ) : (
        <div className="h-full flex flex-col items-center justify-center p-6">
          <Card className="max-w-md md:max-w-lg lg:max-w-2xl w-full">
            <CardContent className="pt-6 pb-6 text-center">
              <FileUp className="h-12 w-12 md:h-16 md:w-16 text-muted-foreground/50 mx-auto mb-3 md:mb-5" />
              <h3 className="text-lg md:text-xl lg:text-2xl font-medium mb-2 md:mb-3">No Content Available</h3>
              <p className="text-muted-foreground text-sm md:text-base max-w-md mx-auto">
                There seems to be no content available for this document.
              </p>
            </CardContent>
          </Card>
        </div>
      )}
      
      {/* Drag Drop Overlay */}
      {dragActive && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
          <Card className="max-w-sm mx-auto animate-slide-in p-6">
            <div className="mb-6 bg-accent/10 w-20 h-20 rounded-full flex items-center justify-center mx-auto">
              <FileUp className="h-10 w-10 text-accent" />
            </div>
            <CardTitle className="text-center mb-2">Append to Document</CardTitle>
            <CardDescription className="text-center mb-4">
              Release to upload and append this file to your document
            </CardDescription>
            <CardFooter className="pt-0 justify-center">
              <p className="text-xs text-muted-foreground">
                Supports PDF, JPG, PNG files
              </p>
            </CardFooter>
          </Card>
        </div>
      )}
    </div>
  );
}
