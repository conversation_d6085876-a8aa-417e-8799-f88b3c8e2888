import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "wouter";
import { <PERSON>Question, ArrowLeft } from "lucide-react";
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

export default function NotFound() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center bg-background animate-fade-in">
      <Card className="max-w-md w-full shadow-sm">
        <CardHeader>
          <div className="w-20 h-20 mx-auto bg-muted/40 rounded-full flex items-center justify-center mb-4">
            <FileQuestion className="h-10 w-10 text-muted-foreground" />
          </div>
          <CardTitle className="text-2xl">Page Not Found</CardTitle>
        </CardHeader>
        
        <CardContent className="pb-6">
          <p className="text-muted-foreground">
            The page you're looking for doesn't exist or has been moved.
          </p>
        </CardContent>
        
        <CardFooter className="flex justify-center pt-0">
          <Link href="/">
            <Button variant="outline" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Return to Homepage
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}
