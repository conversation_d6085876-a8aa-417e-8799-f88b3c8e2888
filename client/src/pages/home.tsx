import { useState, useEffect, useRef } from "react";
import { useQuery } from "@tanstack/react-query";
import { queryClient } from "@/lib/queryClient";
import DocumentViewer from "@/components/document-viewer";
import DocumentList from "@/components/document-list";
import EmptyState from "@/components/empty-state";
import { useToast } from "@/hooks/use-toast";
import { useOcr } from "@/hooks/use-ocr";
import { Document } from "@shared/schema";
import { useIsMobile as useMobile } from "@/hooks/use-mobile";
import { deleteDocument, updateDocument } from "@/lib/api";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Menu, FileText, ChevronLeft, ChevronRight } from "lucide-react";

export default function Home() {
  const [apiKey, setApiKey] = useState<string>(localStorage.getItem("mistral_api_key") || "");
  const [useSharedKey, setUseSharedKey] = useState<boolean>(localStorage.getItem("use_shared_key") === "true");
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [viewMode, setViewMode] = useState<"rendered" | "raw">("rendered");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const { toast } = useToast();
  const isMobile = useMobile();
  const refreshSharedKeyStatusRef = useRef<(() => void) | null>(null);
  
  const { 
    uploadDocument,
    uploadMultipleDocuments,
    appendToDocument,
    appendMultipleToDocument,
    isUploading 
  } = useOcr({ 
    apiKey,
    useSharedKey,
    onUploadSuccess: (document) => {
      setSelectedDocument(document);
      setCurrentPage(1);
      queryClient.invalidateQueries({ queryKey: ['/api/documents'] });
      toast({
        title: "Document processed successfully",
        description: "The document has been converted to markdown format."
      });
    },
    onUploadError: (error) => {
      toast({
        title: "Processing failed",
        description: error.message || "Failed to process document. Please try again.",
        variant: "destructive"
      });
    },
    onMultipleUploadSuccess: (document) => {
      setSelectedDocument(document);
      setCurrentPage(1);
      queryClient.invalidateQueries({ queryKey: ['/api/documents'] });
      toast({
        title: "Documents processed successfully",
        description: `Combined ${document.totalPages} pages into one document.`
      });
    },
    onMultipleUploadError: (error) => {
      toast({
        title: "Processing failed",
        description: error.message || "Failed to process documents. Please try again.",
        variant: "destructive"
      });
    },
    onAppendSuccess: (updatedDocument) => {
      // Update the selected document with the new version that includes appended pages
      setSelectedDocument(updatedDocument);
      // Navigate to the first of the newly appended pages
      setCurrentPage(updatedDocument.totalPages);
      toast({
        title: "Page appended successfully",
        description: "New page has been processed and added to the document."
      });
    },
    onAppendError: (error) => {
      toast({
        title: "Append failed",
        description: error.message || "Failed to append page. Please try again.",
        variant: "destructive"
      });
    },
    onAppendMultipleSuccess: (updatedDocument) => {
      // Update the selected document with the new version that includes appended pages
      setSelectedDocument(updatedDocument);
      // Navigate to the first of the newly appended pages
      setCurrentPage(updatedDocument.totalPages);
      toast({
        title: "Pages appended successfully",
        description: `${updatedDocument.totalPages - (selectedDocument?.totalPages || 0)} new pages have been processed and added to the document.`
      });
    },
    onAppendMultipleError: (error) => {
      toast({
        title: "Append failed",
        description: error.message || "Failed to append pages. Please try again.",
        variant: "destructive"
      });
    }
  });

  // Fetch documents
  const { data: documents = [], isLoading } = useQuery<Document[]>({
    queryKey: ['/api/documents'],
  });

  const validateApiKey = () => {
    if (!apiKey && !useSharedKey) {
      toast({
        title: "API Key Required",
        description: "Please either enter your Mistral API key or enable the shared key option.",
        variant: "destructive"
      });
      return false;
    }
    return true;
  };

  const handleFileUpload = async (file: File) => {
    if (!validateApiKey()) {
      return;
    }
    
    await uploadDocument(file);
    
    // Refresh shared key status after processing
    if (useSharedKey && refreshSharedKeyStatusRef?.current) {
      refreshSharedKeyStatusRef.current();
    }
  };

  const handleMultipleFileUpload = async (files: File[]) => {
    if (!validateApiKey()) {
      return;
    }
    
    await uploadMultipleDocuments(files);
    
    // Refresh shared key status after processing
    if (useSharedKey && refreshSharedKeyStatusRef?.current) {
      refreshSharedKeyStatusRef.current();
    }
  };

  const handleAppendDocument = async (file: File) => {
    if (!selectedDocument) {
      toast({
        title: "No document selected",
        description: "Please select a document to append to.",
        variant: "destructive"
      });
      return;
    }

    if (!validateApiKey()) {
      return;
    }
    
    // The onAppendSuccess callback will handle page navigation after successful append
    await appendToDocument(selectedDocument.id, file);
    
    // Refresh shared key status after processing
    if (useSharedKey && refreshSharedKeyStatusRef?.current) {
      refreshSharedKeyStatusRef.current();
    }
  };

  const handleAppendMultipleDocuments = async (files: File[]) => {
    if (!selectedDocument) {
      toast({
        title: "No document selected",
        description: "Please select a document to append to.",
        variant: "destructive"
      });
      return;
    }

    if (!validateApiKey()) {
      return;
    }
    
    // The onAppendMultipleSuccess callback will handle page navigation after successful append
    await appendMultipleToDocument(selectedDocument.id, files);
    
    // Refresh shared key status after processing
    if (useSharedKey && refreshSharedKeyStatusRef?.current) {
      refreshSharedKeyStatusRef.current();
    }
  };

  const handleSelectDocument = (document: Document) => {
    setSelectedDocument(document);
    setCurrentPage(1);
  };
  
  const handleDeleteDocument = async (id: number) => {
    if (selectedDocument?.id === id) {
      setSelectedDocument(null);
    }
    try {
      await deleteDocument(id);
      queryClient.invalidateQueries({ queryKey: ['/api/documents'] });
      toast({
        title: "Document deleted",
        description: "Document has been permanently deleted."
      });
    } catch (error) {
      toast({
        title: "Error deleting document",
        description: "Failed to delete the document. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleSaveApiKey = (value: string) => {
    setApiKey(value);
    localStorage.setItem("mistral_api_key", value);
    toast({
      title: "API Key Saved",
      description: "Your API key has been saved locally."
    });
  };
  
  const handleToggleSharedKey = (useShared: boolean) => {
    setUseSharedKey(useShared);
    localStorage.setItem("use_shared_key", useShared ? "true" : "false");
  };
  
  const handleRenameDocument = async (id: number, newTitle: string) => {
    try {
      const updatedDoc = await updateDocument(id, { title: newTitle });
      if (selectedDocument?.id === id) {
        setSelectedDocument(updatedDoc);
      }
      queryClient.invalidateQueries({ queryKey: ['/api/documents'] });
      toast({
        title: "Document renamed",
        description: "Document title has been updated successfully."
      });
    } catch (error) {
      toast({
        title: "Error renaming document",
        description: "Failed to update document title. Please try again.",
        variant: "destructive"
      });
    }
  };

  const renderContent = () => {
    if (!selectedDocument) {
      return <EmptyState onUpload={handleFileUpload} onMultipleUpload={handleMultipleFileUpload} isUploading={isUploading} />;
    }

    return (
      <DocumentViewer
        document={selectedDocument}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        viewMode={viewMode}
        setViewMode={setViewMode}
        onAppend={handleAppendDocument}
        onAppendMultiple={handleAppendMultipleDocuments}
        isProcessing={isUploading}
      />
    );
  };

  // Mobile layout
  if (isMobile) {
    return (
      <div className="flex flex-col h-screen bg-background">
        <header className="flex items-center justify-between px-4 py-3 border-b border-border bg-background shadow-sm">
          <div className="flex items-center space-x-3">
            <FileText className="h-6 w-6 text-accent" />
            <h1 className="text-lg font-semibold">Mistral OCR</h1>
          </div>
          
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon" className="rounded-full">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="p-0 w-[85%] max-w-sm">
              <div className="pt-4 pb-2 px-4 bg-primary text-primary-foreground">
                <div className="flex items-center space-x-2 mb-2">
                  <FileText className="h-5 w-5 text-accent" />
                  <h2 className="text-xl font-medium">Documents</h2>
                </div>
                <p className="text-sm text-primary-foreground/80">
                  Process and convert documents with Mistral OCR
                </p>
              </div>
              <DocumentList
                documents={documents}
                isLoading={isLoading}
                selectedDocument={selectedDocument}
                onSelectDocument={handleSelectDocument}
                onUpload={handleFileUpload}
                onDeleteDocument={handleDeleteDocument}
                onRenameDocument={handleRenameDocument}
                apiKey={apiKey}
                onApiKeyChange={handleSaveApiKey}
                useSharedKey={useSharedKey}
                onUseSharedKeyChange={handleToggleSharedKey}
                refreshSharedKeyStatusRef={refreshSharedKeyStatusRef}
              />
            </SheetContent>
          </Sheet>
        </header>
        
        <main className="flex-grow overflow-auto">
          {renderContent()}
        </main>
        
        {selectedDocument && (
          <footer className="border-t border-border py-2 px-4 bg-background">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium truncate">{selectedDocument.title}</p>
                <div className="flex items-center space-x-2">
                  <p className="text-xs text-muted-foreground">
                    Page {currentPage} of {selectedDocument.totalPages}
                  </p>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 text-xs px-2 text-muted-foreground"
                    onClick={() => {
                      const newTitle = prompt("Enter new document title:", selectedDocument.title);
                      if (newTitle && newTitle.trim() !== "" && newTitle !== selectedDocument.title) {
                        handleRenameDocument(selectedDocument.id, newTitle.trim());
                      }
                    }}
                  >
                    Rename
                  </Button>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Button 
                  variant="outline" 
                  size="icon" 
                  className="h-8 w-8"
                  disabled={currentPage <= 1}
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button 
                  variant="outline" 
                  size="icon" 
                  className="h-8 w-8"
                  disabled={currentPage >= selectedDocument.totalPages}
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, selectedDocument.totalPages))}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs"
                  onClick={() => setViewMode(viewMode === "rendered" ? "raw" : "rendered")}
                >
                  {viewMode === "rendered" ? "Raw" : "Rendered"}
                </Button>
              </div>
            </div>
          </footer>
        )}
      </div>
    );
  }

  // Desktop layout
  return (
    <div className="flex h-screen w-full bg-background">
      <aside className="w-[280px] lg:w-[320px] xl:w-[350px] border-r border-border bg-muted/30 flex flex-col hidden md:block">
        <div className="pt-6 pb-3 px-4 border-b border-border bg-muted/50">
          <div className="flex items-center space-x-2.5 mb-3">
            <FileText className="h-6 w-6 text-accent" />
            <h1 className="text-xl font-semibold">Mistral OCR</h1>
          </div>
          <p className="text-sm text-muted-foreground">
            Process and convert documents using Mistral AI
          </p>
        </div>
        
        <div className="flex-1 flex flex-col overflow-auto">
          <DocumentList
            documents={documents}
            isLoading={isLoading}
            selectedDocument={selectedDocument}
            onSelectDocument={handleSelectDocument}
            onUpload={handleFileUpload}
            onDeleteDocument={handleDeleteDocument}
            onRenameDocument={handleRenameDocument}
            apiKey={apiKey}
            onApiKeyChange={handleSaveApiKey}
            useSharedKey={useSharedKey}
            onUseSharedKeyChange={handleToggleSharedKey}
            refreshSharedKeyStatusRef={refreshSharedKeyStatusRef}
          />
        </div>
      </aside>
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="border-b border-border bg-background px-6 py-3 shadow-sm flex items-center justify-between">
          {selectedDocument ? (
            <div className="flex-1">
              <h2 className="font-medium truncate">{selectedDocument.title}</h2>
              <p className="text-sm text-muted-foreground">
                {selectedDocument.totalPages} page{selectedDocument.totalPages !== 1 ? 's' : ''}
              </p>
            </div>
          ) : (
            <div className="flex-1">
              <p className="text-sm text-muted-foreground">
                Upload a document to get started
              </p>
            </div>
          )}
          
          {selectedDocument && (
            <div className="flex items-center space-x-3 md:space-x-4 lg:space-x-5">
              <div className="flex items-center space-x-1 bg-muted/30 p-1 rounded-md">
                <Button 
                  variant="ghost" 
                  size="sm"
                  className={viewMode === "rendered" ? "bg-background shadow-sm" : ""}
                  onClick={() => setViewMode("rendered")}
                >
                  Rendered
                </Button>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className={viewMode === "raw" ? "bg-background shadow-sm" : ""}
                  onClick={() => setViewMode("raw")}
                >
                  Raw
                </Button>
              </div>
              
              <div className="flex items-center bg-muted/30 p-1 rounded-md">
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-8 w-8"
                  disabled={currentPage <= 1}
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="px-2 text-sm">
                  {currentPage} / {selectedDocument.totalPages}
                </span>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-8 w-8"
                  disabled={currentPage >= selectedDocument.totalPages}
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, selectedDocument.totalPages))}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="flex items-center space-x-2">
                <Button 
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newTitle = prompt("Enter new document title:", selectedDocument.title);
                    if (newTitle && newTitle.trim() !== "" && newTitle !== selectedDocument.title) {
                      handleRenameDocument(selectedDocument.id, newTitle.trim());
                    }
                  }}
                >
                  Rename
                </Button>
                
                <Button 
                  variant="outline"
                  size="sm"
                  className="text-destructive hover:text-destructive hover:bg-destructive/10"
                  onClick={() => {
                    if (window.confirm("Are you sure you want to delete this document?")) {
                      handleDeleteDocument(selectedDocument.id);
                    }
                  }}
                >
                  Delete
                </Button>
              </div>
            </div>
          )}
        </header>
        
        <main className="flex-1 overflow-auto p-4 md:p-6 flex justify-center">
          <div className="w-full max-w-full mx-auto xl:max-w-[90%] 2xl:max-w-[85%]">
            {renderContent()}
          </div>
        </main>
      </div>
    </div>
  );
}
