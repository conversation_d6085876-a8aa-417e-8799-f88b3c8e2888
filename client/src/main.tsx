import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";

// Add Nord theme colors to Tailwind
document.documentElement.style.setProperty('--background', '0 0% 100%');
document.documentElement.style.setProperty('--foreground', '222 16% 28%'); // #3B4252
document.documentElement.style.setProperty('--primary', '220 16% 22%'); // #2E3440
document.documentElement.style.setProperty('--primary-foreground', '0 0% 100%');
document.documentElement.style.setProperty('--secondary', '220 13% 35%'); // #4C566A
document.documentElement.style.setProperty('--secondary-foreground', '0 0% 100%');
document.documentElement.style.setProperty('--muted', '210 13% 90%');
document.documentElement.style.setProperty('--muted-foreground', '215 13% 40%');
document.documentElement.style.setProperty('--accent', '195 47% 68%'); // #88C0D0
document.documentElement.style.setProperty('--accent-foreground', '220 16% 22%');
document.documentElement.style.setProperty('--border', '220 16% 90%');
document.documentElement.style.setProperty('--input', '220 16% 90%');
document.documentElement.style.setProperty('--card', '0 0% 100%');
document.documentElement.style.setProperty('--card-foreground', '222 16% 28%');
document.documentElement.style.setProperty('--destructive', '0 63% 56%');
document.documentElement.style.setProperty('--destructive-foreground', '0 0% 100%');
document.documentElement.style.setProperty('--ring', '195 47% 68%');

createRoot(document.getElementById("root")!).render(<App />);
