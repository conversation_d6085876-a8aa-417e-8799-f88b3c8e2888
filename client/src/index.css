@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Nord-inspired Color Scheme */
  --background: 220 16% 98%; /* #F9FAFB - Background */
  --foreground: 220 16% 22%; /* #2E3440 - Text */
  
  --muted: 218 27% 94%; /* #ECEFF4 - Subtle backgrounds */
  --muted-foreground: 220 14% 49%; /* #7B86A2 - Muted text */
  
  --popover: 220 16% 98%; /* Same as background */
  --popover-foreground: 220 16% 22%; /* Same as foreground */
  
  --card: 220 16% 98%; /* #F9FAFB - Card background */
  --card-foreground: 220 16% 22%; /* #2E3440 - Card text */
  
  --border: 218 13% 86%; /* #D8DEE9 - Borders */
  --input: 218 13% 86%; /* #D8DEE9 - Input fields */
  
  --primary: 220 16% 22%; /* #2E3440 - Primary color */
  --primary-foreground: 220 16% 98%; /* #F9FAFB - Text on primary */
  
  --secondary: 220 13% 36%; /* #4C566A - Secondary color */
  --secondary-foreground: 220 16% 98%; /* #F9FAFB - Text on secondary */
  
  --accent: 195 47% 67%; /* #88C0D0 - Accent color */
  --accent-foreground: 220 16% 22%; /* #2E3440 - Text on accent */
  
  --destructive: 354 42% 56%; /* #BF616A - Error/destructive */
  --destructive-foreground: 220 16% 98%; /* #F9FAFB - Text on destructive */
  
  --success: 92 48% 65%; /* #A3BE8C - Success color */
  
  --ring: 195 47% 67%; /* #88C0D0 - Focus ring */
  
  /* Radius and Spacing */
  --radius: 0.5rem;
  --spacing-1: 4px;
  --spacing-2: 8px;
  --spacing-3: 12px;
  --spacing-4: 16px;
  --spacing-6: 24px;
  --spacing-8: 32px;
  --spacing-12: 48px;
  --spacing-16: 64px;
  --spacing-24: 96px;
}

.dark {
  --background: 220 16% 15%; /* Dark background */
  --foreground: 218 27% 94%; /* #ECEFF4 - Light text */
  
  --muted: 220 16% 22%; /* #2E3440 - Dark muted background */
  --muted-foreground: 218 10% 70%; /* Muted text on dark */
  
  --popover: 220 16% 15%; /* Same as background */
  --popover-foreground: 218 27% 94%; /* Same as foreground */
  
  --card: 220 13% 18%; /* Dark card background */
  --card-foreground: 218 27% 94%; /* Light text on card */
  
  --border: 220 13% 30%; /* Dark borders */
  --input: 220 13% 30%; /* Dark input fields */
  
  --primary: 218 27% 94%; /* Light primary color */
  --primary-foreground: 220 16% 15%; /* Dark text on primary */
  
  --secondary: 220 13% 75%; /* Light secondary color */
  --secondary-foreground: 220 16% 15%; /* Dark text on secondary */
  
  --accent: 195 47% 67%; /* Same accent color */
  --accent-foreground: 220 16% 15%; /* Dark text on accent */
  
  --destructive: 354 42% 56%; /* Same destructive color */
  --destructive-foreground: 218 27% 94%; /* Light text on destructive */
  
  --success: 92 48% 65%; /* Same success color */
  
  --ring: 195 47% 67%; /* Same focus ring */
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    line-height: 1.5;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-medium tracking-tight;
    line-height: 1.2;
  }

  h1 {
    @apply text-2xl sm:text-3xl lg:text-4xl;
  }

  h2 {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }

  h3 {
    @apply text-lg sm:text-xl;
  }

  h4 {
    @apply text-base sm:text-lg font-medium;
  }

  a {
    @apply text-accent hover:text-accent/80 transition-colors;
  }

  /* Container classes for consistent spacing */
  .container-sm {
    @apply w-full px-4 sm:px-6 mx-auto max-w-screen-sm;
  }

  .container-md {
    @apply w-full px-4 sm:px-6 mx-auto max-w-3xl;
  }

  .container-lg {
    @apply w-full px-4 sm:px-6 lg:px-8 mx-auto max-w-screen-lg;
  }

  .container-xl {
    @apply w-full px-4 sm:px-6 lg:px-8 mx-auto max-w-screen-xl;
  }
}

/* Component-specific styles */
@layer components {
  /* Card improvements */
  .card {
    @apply bg-card border border-border rounded-lg shadow-sm transition-all duration-200;
  }
  
  .card-hover {
    @apply hover:shadow-md hover:border-accent/30 cursor-pointer;
  }
  
  /* Button improvements */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors 
    focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2
    disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }
  
  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }
  
  .btn-accent {
    @apply bg-accent text-accent-foreground hover:bg-accent/90;
  }
  
  .btn-ghost {
    @apply hover:bg-accent/10 hover:text-accent;
  }
  
  .btn-sm {
    @apply h-8 px-3 rounded-md;
  }
  
  .btn-md {
    @apply h-10 px-4 py-2;
  }
  
  .btn-lg {
    @apply h-12 px-6 rounded-md;
  }
  
  /* Form improvements */
  .form-group {
    @apply mb-4;
  }
  
  .form-label {
    @apply block text-sm font-medium mb-2;
  }
  
  .form-input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm
    placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2
    focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed
    disabled:opacity-50;
  }
  
  /* Spacing utilities */
  .space-y-sm {
    @apply space-y-4;
  }
  
  .space-y-md {
    @apply space-y-6;
  }
  
  .space-y-lg {
    @apply space-y-8;
  }
  
  .space-x-sm {
    @apply space-x-4;
  }
  
  .space-x-md {
    @apply space-x-6;
  }
  
  .space-x-lg {
    @apply space-x-8;
  }
  
  /* Document viewer styles */
  .document-content {
    @apply prose prose-stone max-w-none prose-headings:font-medium prose-headings:tracking-tight 
    prose-a:text-accent prose-a:font-normal prose-a:no-underline hover:prose-a:text-accent/80
    prose-code:font-normal prose-code:before:content-none prose-code:after:content-none;
  }
  
  /* Animation classes */
  .animate-fade-in {
    @apply animate-in fade-in duration-300;
  }
  
  .animate-slide-in {
    @apply animate-in slide-in-from-bottom-4 duration-300;
  }
  
  .animate-scale-in {
    @apply animate-in zoom-in-95 duration-300;
  }
  
  /* Responsive grid system for document list */
  .document-grid {
    @apply grid gap-4 sm:gap-6;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
  
  /* Progress indicators */
  .progress-bar {
    @apply h-2 w-full overflow-hidden rounded-full bg-muted;
  }
  
  .progress-bar-fill {
    @apply h-full bg-accent rounded-full transition-all duration-300 ease-in-out;
  }
  
  /* Markdown content styles */
  .markdown-content {
    @apply text-foreground leading-relaxed;
  }
  
  .markdown-content img {
    @apply rounded-md shadow-sm my-4 max-w-full h-auto;
  }
}

