import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Document } from "@shared/schema";
import { uploadDocument, appendToDocument, uploadMultipleDocuments, appendMultipleToDocument } from "@/lib/api";

interface UseOcrOptions {
  apiKey?: string;
  useSharedKey?: boolean;
  onUploadSuccess?: (document: Document) => void;
  onUploadError?: (error: Error) => void;
  onAppendSuccess?: (document: Document) => void;
  onAppendError?: (error: Error) => void;
  onMultipleUploadSuccess?: (document: Document) => void;
  onMultipleUploadError?: (error: Error) => void;
  onAppendMultipleSuccess?: (document: Document) => void;
  onAppendMultipleError?: (error: Error) => void;
}

export function useOcr({
  apiKey,
  useSharedKey = false,
  onUploadSuccess,
  onUploadError,
  onAppendSuccess,
  onAppendError,
  onMultipleUploadSuccess,
  onMultipleUploadError,
  onAppendMultipleSuccess,
  onAppendMultipleError
}: UseOcrOptions) {
  const queryClient = useQueryClient();
  const [isUploading, setIsUploading] = useState(false);
  
  // Upload document mutation
  const uploadMutation = useMutation({
    mutationFn: (file: File) => uploadDocument(file, apiKey, useSharedKey),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/documents'] });
      if (onUploadSuccess) onUploadSuccess(data);
    },
    onError: (error: Error) => {
      if (onUploadError) onUploadError(error);
    }
  });
  
  // Multiple upload mutation
  const multipleUploadMutation = useMutation({
    mutationFn: (files: File[]) => uploadMultipleDocuments(files, apiKey, useSharedKey),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/documents'] });
      if (onMultipleUploadSuccess) onMultipleUploadSuccess(data);
    },
    onError: (error: Error) => {
      if (onMultipleUploadError) onMultipleUploadError(error);
    }
  });
  
  // Append to document mutation
  const appendMutation = useMutation({
    mutationFn: ({ documentId, file }: { documentId: number, file: File }) => 
      appendToDocument(documentId, file, apiKey, useSharedKey),
    onSuccess: (data) => {
      console.log('Append success, updated document:', data);
      queryClient.invalidateQueries({ queryKey: ['/api/documents'] });
      // Also invalidate the specific document to ensure it's refetched with new pages
      queryClient.invalidateQueries({ queryKey: [`/api/documents/${data.id}`] });
      if (onAppendSuccess) onAppendSuccess(data);
    },
    onError: (error: Error) => {
      console.error('Append error:', error);
      if (onAppendError) onAppendError(error);
    }
  });
  
  // Append multiple files to document mutation
  const appendMultipleMutation = useMutation({
    mutationFn: ({ documentId, files }: { documentId: number, files: File[] }) => 
      appendMultipleToDocument(documentId, files, apiKey, useSharedKey),
    onSuccess: (data) => {
      console.log('Append multiple success, updated document:', data);
      queryClient.invalidateQueries({ queryKey: ['/api/documents'] });
      // Also invalidate the specific document to ensure it's refetched with new pages
      queryClient.invalidateQueries({ queryKey: [`/api/documents/${data.id}`] });
      if (onAppendMultipleSuccess) onAppendMultipleSuccess(data);
    },
    onError: (error: Error) => {
      console.error('Append multiple error:', error);
      if (onAppendMultipleError) onAppendMultipleError(error);
    }
  });
  
  // Wrapper function for upload that sets loading state
  const handleUploadDocument = async (file: File) => {
    try {
      setIsUploading(true);
      return await uploadMutation.mutateAsync(file);
    } finally {
      setIsUploading(false);
    }
  };
  
  // Wrapper function for multiple uploads that sets loading state
  const handleUploadMultipleDocuments = async (files: File[]) => {
    try {
      setIsUploading(true);
      return await multipleUploadMutation.mutateAsync(files);
    } finally {
      setIsUploading(false);
    }
  };
  
  // Wrapper function for append that sets loading state
  const handleAppendToDocument = async (documentId: number, file: File) => {
    try {
      setIsUploading(true);
      return await appendMutation.mutateAsync({ documentId, file });
    } finally {
      setIsUploading(false);
    }
  };
  
  // Wrapper function for append multiple that sets loading state
  const handleAppendMultipleToDocument = async (documentId: number, files: File[]) => {
    try {
      setIsUploading(true);
      return await appendMultipleMutation.mutateAsync({ documentId, files });
    } finally {
      setIsUploading(false);
    }
  };
  
  return {
    uploadDocument: handleUploadDocument,
    uploadMultipleDocuments: handleUploadMultipleDocuments,
    appendToDocument: handleAppendToDocument,
    appendMultipleToDocument: handleAppendMultipleToDocument,
    isUploading: isUploading || uploadMutation.isPending || appendMutation.isPending || multipleUploadMutation.isPending || appendMultipleMutation.isPending
  };
}
