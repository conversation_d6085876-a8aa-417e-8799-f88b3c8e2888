# Mistral OCR - Document to Markdown Converter

## Overview

This is a full-stack web application that converts documents (PDFs and images) to searchable markdown format using Mistral AI's OCR API. The application features a React frontend with TypeScript, an Express.js backend, and uses Drizzle ORM with PostgreSQL for data persistence. Users can upload documents, view them in both rendered and raw markdown formats, and manage multiple documents with page-by-page navigation.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript and Vite for development
- **UI Components**: Comprehensive design system using Radix UI primitives with shadcn/ui styling
- **Styling**: Tailwind CSS with custom Nord-inspired color theme and CSS variables
- **State Management**: TanStack Query for server state management and caching
- **Routing**: Wouter for lightweight client-side routing
- **Form Handling**: React Hook Form with Zod validation for type-safe forms

### Backend Architecture
- **Framework**: Express.js with TypeScript in ESM format
- **API Design**: RESTful API with structured error handling and request logging middleware
- **File Processing**: Multer for multipart file upload handling
- **Rate Limiting**: Custom in-memory rate limiter for shared API key usage
- **Error Handling**: Centralized error handling with proper HTTP status codes

### Data Storage Solutions
- **Database**: PostgreSQL with Drizzle ORM for type-safe database operations
- **Schema Design**: 
  - Users table for basic authentication
  - Documents table storing processed OCR results as JSON with metadata
  - Document pages stored as structured JSON with markdown content and image data
- **Connection**: Neon Database serverless PostgreSQL instance
- **Migrations**: Drizzle Kit for schema migrations and database management

### Authentication and Authorization
- **Session Management**: Connect-pg-simple for PostgreSQL-backed sessions
- **API Key Management**: Support for both user-provided Mistral API keys and shared API key with rate limiting
- **Rate Limiting**: IP-based rate limiting for shared API key usage (10 requests per hour)

### External Dependencies
- **Mistral AI OCR API**: Primary OCR service for document processing with support for PDFs and images
- **Document Processing**: Handles complex layouts, preserves formatting, and extracts images
- **File Support**: PDF documents and image formats (JPG, PNG, JPEG, GIF)
- **Markdown Rendering**: ReactMarkdown with GitHub Flavored Markdown support for rich content display
- **UI Components**: Extensive Radix UI component library for accessible, unstyled primitives
- **Development Tools**: Replit-specific plugins for development environment integration

The application follows a clean separation of concerns with shared TypeScript types between frontend and backend, comprehensive error handling, and a responsive design that works across desktop and mobile devices.