# Dependencies
node_modules

# Build outputs
dist
server/public

# Environment variables
.env
.env.local
.env.production

# System files
.DS_Store
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Temporary files
*.tmp
*.temp
vite.config.ts.*
*.tar.gz

# Database
*.sqlite
*.db

# Uploads (if storing files locally)
uploads/