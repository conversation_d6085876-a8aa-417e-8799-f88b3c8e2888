import { pgTable, text, serial, integer, boolean, timestamp, json } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const documents = pgTable("documents", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  originalFilename: text("original_filename").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  pages: json("pages").notNull().$type<DocumentPage[]>(),
  totalPages: integer("total_pages").notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export const insertDocumentSchema = createInsertSchema(documents).omit({
  id: true,
  createdAt: true,
});

// Extra schemas and types for the application
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type Document = typeof documents.$inferSelect;
export type InsertDocument = z.infer<typeof insertDocumentSchema>;

export interface DocumentPage {
  index: number;
  markdown: string;
  images: DocumentImage[];
  dimensions?: {
    dpi?: number;
    height?: number;
    width?: number;
  };
}

export interface DocumentImage {
  id: string;
  // Support both snake_case (from API) and camelCase versions
  top_left_x?: number; 
  top_left_y?: number;
  bottom_right_x?: number;
  bottom_right_y?: number;
  image_base64?: string;
  
  // Also support camelCase versions
  topLeftX?: number;
  topLeftY?: number;
  bottomRightX?: number;
  bottomRightY?: number;
  imageBase64?: string;
}

export interface OcrResponse {
  pages: DocumentPage[];
}

export const apiKeySchema = z.object({
  apiKey: z.string().optional(),
  useSharedKey: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional(),
}).refine(data => data.apiKey || data.useSharedKey, {
  message: "Either provide an API key or use the shared key option",
  path: ["apiKey"]
});

export const documentFileSchema = z.object({
  file: z.instanceof(File),
});

export const documentAppendSchema = z.object({
  documentId: z.number(),
  file: z.instanceof(File),
});
