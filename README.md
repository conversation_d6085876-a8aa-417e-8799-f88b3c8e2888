# Mistral OCR - Document to Markdown Converter

A full-stack web application that converts documents (PDFs and images) to searchable markdown format using Mistral AI's OCR API.

## Features

- Upload PDF documents and images for OCR processing
- Convert documents to markdown with preserved formatting
- View documents in both rendered and raw markdown formats
- Page-by-page navigation for multi-page documents
- Support for both personal API keys and shared API key with rate limiting
- Responsive design for desktop and mobile

## Tech Stack

- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS + Radix UI
- **Backend**: Express.js + TypeScript
- **Database**: PostgreSQL + Drizzle ORM
- **OCR**: Mistral AI OCR API
- **Deployment**: Docker + Docker Compose

## Prerequisites

- Node.js 20+
- PostgreSQL 16+ (or use Docker Compose)
- Mistral AI API key

## Quick Start with Docker

1. Clone the repository:
```bash
git clone <your-repo-url>
cd Mistral-OCR
```

2. Create environment file:
```bash
cp .env.example .env
```

3. Edit `.env` with your configuration:
```env
DATABASE_URL=********************************************/mistral_ocr
MISTRAL_API_KEY=your_mistral_api_key_here
SESSION_SECRET=your_random_session_secret
```

4. Start with Docker Compose:
```bash
npm run docker:run
```

5. Access the application at `http://localhost:5000`

## Manual Setup

1. Install dependencies:
```bash
npm install
```

2. Set up PostgreSQL database and update `.env`

3. Generate and run database migrations:
```bash
npm run db:generate
npm run db:migrate
```

4. Build the application:
```bash
npm run build
```

5. Start the production server:
```bash
npm start
```

## Development

1. Start development server:
```bash
npm run dev
```

2. Access at `http://localhost:5000`

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `DATABASE_URL` | PostgreSQL connection string | Yes |
| `MISTRAL_API_KEY` | Mistral AI API key for OCR | Yes |
| `SESSION_SECRET` | Secret for session encryption | Recommended |
| `NODE_ENV` | Environment (development/production) | No |
| `PORT` | Server port (default: 5000) | No |

## API Endpoints

- `GET /api/documents` - List all documents
- `GET /api/documents/:id` - Get specific document
- `POST /api/documents/upload` - Upload single document
- `POST /api/documents/upload-multiple` - Upload multiple documents
- `POST /api/documents/:id/append` - Append pages to document
- `PATCH /api/documents/:id` - Update document title
- `DELETE /api/documents/:id` - Delete document
- `GET /api/shared-key-status` - Check shared API key status

## Deployment Options

### Docker (Recommended)
Use the provided `docker-compose.yml` for easy deployment with PostgreSQL included.

### Cloud Platforms
- **Vercel/Netlify**: Frontend only (requires separate backend deployment)
- **Railway/Render**: Full-stack deployment
- **AWS/GCP/Azure**: Container deployment

### Database Options
- **Neon Database**: Serverless PostgreSQL (recommended for production)
- **Supabase**: PostgreSQL with additional features
- **Local PostgreSQL**: For development

## License

MIT
